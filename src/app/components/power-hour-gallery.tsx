"use client";

import React from "react";
import { PowerHour, PowerHourID } from "@/models/power-hour";
import PowerHourCard from "./power-hour-card";
import { useRouter } from "next/navigation";
import { useFirestorePowerHours } from "@/hooks/use-firestore-power-hour";
import { FaSync } from "react-icons/fa";
import FirestoreStatus from "./firestore-status";

interface PowerHourGalleryProps {
  limit?: number;
  showHeader?: boolean;
  headerTitle?: string;
  showFirestoreStatus?: boolean;
  onPowerHourClick?: (powerHourId: PowerHourID) => void;
}

const PowerHourGallery: React.FC<PowerHourGalleryProps> = ({
  limit,
  showHeader = true,
  headerTitle = "Power Hours",
  showFirestoreStatus = true,
  onPowerHourClick,
}) => {
  const router = useRouter();
  const { powerHours, loading, error, connectionError, firestoreError } = useFirestorePowerHours(limit);

  // Debug logging
  console.log("PowerHourGallery: Received data:", {
    powerHoursCount: powerHours?.length || 0,
    loading,
    error: error?.message,
    connectionError: connectionError?.message,
    firestoreError: firestoreError?.message,
    limit
  });
console.log(powerHours)
  // Default click handler if none provided
  const handlePowerHourClick = (powerHourId: PowerHourID) => {
    if (onPowerHourClick) {
      onPowerHourClick(powerHourId);
    } else {
      // Default navigation to details page
      router.push(`/power-hour-ai/power-hour-details?powerHourId=${powerHourId}`);
    }
  };

  // Determine error information for display
  const getErrorInfo = () => {
    if (connectionError) {
      return {
        type: 'connection' as const,
        title: 'Connection Error',
        message: 'Unable to connect to the database. Please check your internet connection.',
        details: connectionError.message
      };
    }
    if (firestoreError) {
      return {
        type: 'firestore' as const,
        title: 'Database Error',
        message: 'There was an error accessing the power hours database.',
        details: firestoreError.message
      };
    }
    if (error) {
      return {
        type: 'general' as const,
        title: 'Error',
        message: 'An unexpected error occurred while loading power hours.',
        details: error.message
      };
    }
    return null;
  };

  const errorInfo = getErrorInfo();

  return (
    <div className="w-full">
      {/* Header */}
      {showHeader && (
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">{headerTitle}</h2>
          {showFirestoreStatus && <FirestoreStatus />}
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center space-y-4">
            <FaSync className="animate-spin text-4xl text-blue-400" />
            <p className="text-white text-lg">Loading power hours...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {!loading && errorInfo && (
        <div className="flex items-center justify-center py-12">
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-8 max-w-md text-center">
            <div className="text-red-400 text-4xl mb-4">⚠️</div>
            <h3 className="text-xl font-bold text-red-400 mb-2">{errorInfo.title}</h3>
            <p className="text-red-300 mb-4">{errorInfo.message}</p>
            {errorInfo.details && (
              <details className="text-sm text-red-200 mb-4">
                <summary className="cursor-pointer hover:text-red-100">Technical Details</summary>
                <p className="mt-2 font-mono text-xs bg-red-900/30 p-2 rounded">
                  {errorInfo.details}
                </p>
              </details>
            )}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Gallery Grid - Mobile-first responsive design */}
      {!loading && !errorInfo && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {powerHours?.length > 0 ? (
            powerHours.map((powerHour) => (
              <PowerHourCard
                key={powerHour.id}
                powerHour={powerHour}
                onClick={() => handlePowerHourClick(powerHour.id)}
              />
            ))
          ) : (
            <div className="text-center col-span-full py-12">
              <div className="bg-gray-800 rounded-lg p-6 sm:p-8 text-white">
                <h3 className="text-xl sm:text-2xl font-bold mb-4">No Power Hours Yet</h3>
                <p className="text-gray-300 mb-6 text-sm sm:text-base">
                  Be the first to create a power hour! Use the search above to get started.
                </p>
                <div className="text-3xl sm:text-4xl mb-4">🎵</div>
                <p className="text-xs sm:text-sm text-gray-400">
                  Power hours will appear here once they're created and completed.
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PowerHourGallery;
