"use client";

import { PowerHour, PowerHourID } from "@/models/power-hour";
import React, { useEffect, useState } from "react";
import PowerHourCard from "./power-hour-card";
import { useRouter } from "next/navigation";
import { useFirestorePowerHours } from "@/hooks/use-firestore-power-hour";
import { FaSync } from "react-icons/fa";
import FirestoreStatus from "./firestore-status";

const Landing = ({
  search,
  setSearch,
  count,
  setCount,
  onSearchSubmit,
}: {
  search: string;
  setSearch: any;
  count: number;
  setCount: any;
  onSearchSubmit?: (searchTerm: string) => void;
}) => {
  const router = useRouter();
  const [searchValue, setSearchValue] = useState(search);

  // Use the Firestore hook to fetch all power hours directly
  // Note: We don't need a separate state because the hook already manages loading state
  const { powerHours, loading, error, connectionError, firestoreError } = useFirestorePowerHours();

  // Log any errors from Firestore and debug information
  useEffect(() => {
    if (error) {
      console.error("Error fetching power hours:", error);
    }
    if (connectionError) {
      console.error("Connection error:", connectionError);
    }
    if (firestoreError) {
      console.error("Firestore error:", firestoreError);
    }
    console.log("Power hours data:", { powerHours, loading, error, count: powerHours?.length });
  }, [powerHours, loading, error, connectionError, firestoreError]);

  const handleInputChange = (event: any) => {
    setSearchValue(event.target.value);
  };

  // Handler for the Generate button click
  const handleGenerateClick = () => {
    // If onSearchSubmit is provided, use it; otherwise just update the search state
    if (onSearchSubmit && typeof onSearchSubmit === "function") {
      onSearchSubmit(searchValue);
    } else {
      setSearch(searchValue);
    }
  };

  const handleKeyPress = (event: any) => {
    if (event.key === "Enter") {
      handleGenerateClick();
    }
  };

  const countSelectionChange = (keys: any) => {
    // Properly handle the NextUI selection object
    if (keys instanceof Set && keys.size > 0) {
      // Get the first key from the Set and parse it as a number
      const selectedKey = Array.from(keys)[0];
      if (typeof selectedKey === "string") {
        const numericValue = parseInt(selectedKey, 10);
        if (!isNaN(numericValue)) {
          setCount(numericValue);
        }
      }
    }
  };

  const goToActivePowerHour = (powerHourId: PowerHourID) => {
    router.push(`/power-hour-ai/active-power-hour?powerHourId=${powerHourId}`);
  };

  const goToDetails = (powerHourId: PowerHourID) => {
    router.push(`/power-hour-ai/power-hour-details?powerHourId=${powerHourId}`);
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  // Debug function to test Firestore connection
  const testFirestoreConnection = async () => {
    try {
      console.log("Testing Firestore connection...");
      const { firestore, COLLECTIONS } = await import("@/app/firebase/firebase");
      const { collection, getDocs } = await import("firebase/firestore");

      if (!firestore) {
        console.error("Firestore not initialized");
        return;
      }

      const snapshot = await getDocs(collection(firestore, COLLECTIONS.POWER_HOURS));
      console.log("Firestore test successful. Documents found:", snapshot.size);
      snapshot.forEach((doc) => {
        console.log("Document:", doc.id, doc.data());
      });
    } catch (error) {
      console.error("Firestore test failed:", error);
    }
  };

  // Add test button in development - use state to avoid hydration mismatch
  const [isDevelopment, setIsDevelopment] = useState(false);

  useEffect(() => {
    setIsDevelopment(process.env.NODE_ENV === 'development');
  }, []);

  // Helper function to get user-friendly error messages
  const getErrorMessage = (error: any, connectionError: any, firestoreError: any) => {
    if (connectionError) {
      if (connectionError.message.includes("not initialized")) {
        return {
          title: "Database Connection Failed",
          message: "Unable to connect to the database. Please check your internet connection and try again.",
          technical: connectionError.message,
          type: "connection"
        };
      }
      return {
        title: "Connection Error",
        message: "There was a problem connecting to our servers. Please try again in a moment.",
        technical: connectionError.message,
        type: "connection"
      };
    }

    if (firestoreError) {
      if (firestoreError.message.includes("permission-denied")) {
        return {
          title: "Access Denied",
          message: "You don't have permission to access this data. Please sign in or contact support.",
          technical: firestoreError.message,
          type: "permission"
        };
      }
      if (firestoreError.message.includes("unavailable")) {
        return {
          title: "Service Unavailable",
          message: "The database service is temporarily unavailable. Please try again later.",
          technical: firestoreError.message,
          type: "service"
        };
      }
      if (firestoreError.message.includes("network")) {
        return {
          title: "Network Error",
          message: "Unable to reach our servers. Please check your internet connection.",
          technical: firestoreError.message,
          type: "network"
        };
      }
      return {
        title: "Database Error",
        message: "There was a problem loading your power hours from the database.",
        technical: firestoreError.message,
        type: "database"
      };
    }

    if (error) {
      return {
        title: "Unknown Error",
        message: "An unexpected error occurred. Please try refreshing the page.",
        technical: error.message,
        type: "unknown"
      };
    }

    return null;
  };

  const errorInfo = getErrorMessage(error, connectionError, firestoreError);

  return (
    <div className="flex flex-col h-screen overflow-auto">
      <section className="min-h-screen w-full flex flex-col items-center justify-center bg-gray-800 text-white py-8">
        <div className="w-full max-w-6xl mx-auto flex flex-col items-center justify-center px-4">
          <div className="text-5xl font-bold mb-16">Power Hour AI</div>
          <div className="flex items-center w-full max-w-md">
            <input
              type="text"
              placeholder="Search..."
              className="form-input w-full px-4 py-2 border rounded-md shadow-sm text-gray-900 mr-4"
              value={searchValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
            />

            <button
              className={`px-6 py-2 rounded-md transition duration-150 ${
                searchValue
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-blue-300 text-gray-200"
              }`}
              onClick={handleGenerateClick}
              onSubmit={handleGenerateClick}
              disabled={!searchValue} // Disable button when searchValue is empty
            >
              Generate
            </button>
          </div>
          <div className="w-full max-w-md mb-10 mt-6">
            <div className="flex flex-col items-center">
              <div className="text-white mb-3 font-medium">
                Select number of videos:
              </div>
              <div className="flex flex-wrap justify-center gap-3">
                {[2, 5, 10, 30, 60].map((value) => (
                  <button
                    key={value}
                    onClick={() => setCount(value)}
                    className={`px-3 py-1 rounded-md transition duration-150 ${
                      count === value
                        ? "bg-blue-600 text-white"
                        : "bg-gray-700 text-white border border-gray-600 hover:bg-gray-600"
                    }`}
                  >
                    {value}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Example Cards Section */}
          <div className="flex flex-wrap justify-center items-stretch gap-6 w-full max-w-4xl mt-8">
            {/* Example Card 1 */}
            <div
              onClick={() =>
                setSearchValue("Top songs from each EDM sub genre")
              }
              className="flex flex-col items-center justify-between bg-gray-700 border border-gray-600 rounded-lg shadow-md p-4 w-full sm:w-1/3 cursor-pointer"
            >
              <h3 className="text-lg font-semibold mb-2">Be Creative</h3>
              <p>{'"Top songs from each EDM sub genre"'}</p>
            </div>

            {/* Example Card 2 */}
            <div
              onClick={() => setSearchValue("Only Taylor Swift")}
              className="flex flex-col items-center justify-start bg-gray-700 border border-gray-600 rounded-lg shadow-lg p-5 w-full sm:w-1/3 cursor-pointer hover:bg-gray-600 transition-colors duration-300"
            >
              <h3 className="text-lg font-semibold mb-2">By Artist</h3>
              <p>{'"Only Taylor Swift"'}</p>
            </div>

            {/* Example Card 3 */}
            <div
              onClick={() => setSearchValue("Grunge of the 90s")}
              className="flex flex-col items-center justify-between bg-gray-700 border border-gray-600 rounded-lg shadow-lg p-5 w-full sm:w-1/3 cursor-pointer hover:bg-gray-600 transition-colors duration-300"
            >
              <h3 className="text-lg font-semibold mb-2">From an Era</h3>
              <p>{'"Grunge of the 90s"'}</p>
            </div>
          </div>
        </div>
      </section>
      <section className="min-h-screen w-full flex flex-col items-center bg-gray-700 py-12">
        <div className="w-full max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-center mb-8">
            <h2 className="text-3xl font-bold text-white text-center">
              All Power Hours
              {!loading && powerHours && (
                <span className="text-lg font-normal text-gray-300 ml-2">
                  ({powerHours.length})
                </span>
              )}
            </h2>
            <button
              onClick={handleRefresh}
              className="ml-4 p-2 bg-gray-600 hover:bg-gray-500 rounded-lg transition-colors duration-200"
              title="Refresh power hours"
            >
              <FaSync className="text-white" />
            </button>
            {isDevelopment && (
              <button
                onClick={testFirestoreConnection}
                className="ml-2 px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded text-white text-sm"
                title="Test Firestore connection"
              >
                Test DB
              </button>
            )}
          </div>

          {/* Firestore Status Component - shows connection issues */}
          <div className="w-full mb-6">
            <FirestoreStatus
              error={error}
              connectionError={connectionError}
              firestoreError={firestoreError}
              loading={loading}
              showDetails={isDevelopment || !!errorInfo}
            />
          </div>

          <div className="w-full">
            {loading ? (
              <div className="flex flex-col items-center justify-center w-full h-40">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mb-4"></div>
                <p className="text-white text-lg">Loading power hours...</p>
              </div>
            ) : errorInfo ? (
              <div className="text-center w-full max-w-2xl mx-auto">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                  <div className="flex items-center justify-center mb-4">
                    <div className="bg-red-100 rounded-full p-3">
                      <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-red-800 mb-2">{errorInfo.title}</h3>
                  <p className="text-red-700 mb-4">{errorInfo.message}</p>

                  {isDevelopment && (
                    <details className="mb-4">
                      <summary className="text-sm text-red-600 cursor-pointer hover:text-red-800">
                        Technical Details (Development)
                      </summary>
                      <div className="mt-2 p-3 bg-red-100 rounded text-xs text-red-800 font-mono text-left">
                        <p><strong>Error Type:</strong> {errorInfo.type}</p>
                        <p><strong>Message:</strong> {errorInfo.technical}</p>
                        {connectionError && <p><strong>Connection Error:</strong> {connectionError.message}</p>}
                        {firestoreError && <p><strong>Firestore Error:</strong> {firestoreError.message}</p>}
                      </div>
                    </details>
                  )}

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <button
                      onClick={() => window.location.reload()}
                      className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      Retry
                    </button>
                    {errorInfo.type === 'connection' && (
                      <button
                        onClick={testFirestoreConnection}
                        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Test Connection
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {powerHours?.length > 0 ? (
                  powerHours.map((powerHour) => (
                    <PowerHourCard
                      key={powerHour.id}
                      powerHour={powerHour}
                      onClick={() => goToDetails(powerHour.id)}
                    />
                  ))
                ) : (
                  <div className="text-center col-span-full py-12">
                    <div className="bg-gray-800 rounded-lg p-8 text-white">
                      <h3 className="text-2xl font-bold mb-4">No Power Hours Yet</h3>
                      <p className="text-gray-300 mb-6">
                        Be the first to create a power hour! Use the search above to get started.
                      </p>
                      <div className="text-4xl mb-4">🎵</div>
                      <p className="text-sm text-gray-400">
                        Power hours will appear here once they're created and completed.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landing;
