import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loading from "@/app/power-hour-ai/loading";
import PHEntryFeedbackCard from "../feed-back/ph-entry-feedback-card";
import { useFirestorePowerHour } from "@/hooks/use-firestore-power-hour";
import { Button } from "@nextui-org/react";
import PowerHourEntryCard from "../power-hour-entry-card";
import FirestoreStatus from "../firestore-status";
import ClientOnly from "../utils/client-only";

const PowerHourDetails = () => {
  const [powerHourId, setPowerHourId] = useState("");
  const router = useRouter();
  const { powerHour, loading, error, connectionError, firestoreError } = useFirestorePowerHour(powerHourId);

  const searchParams = useSearchParams();
  useEffect(() => {
    const powerHourId = searchParams.get("powerHourId");
    if (powerHourId && powerHourId !== "") setPowerHourId(powerHourId);
  }, []);

  const onStartThisPowerHour = () => {
    router.push(`/power-hour-ai/active-power-hour?powerHourId=${powerHourId}`);
  };

  return (
    <ClientOnly fallback={<Loading />}>
      <div className="flex flex-col h-full items-center bg-gray-800 text-white overflow-hidden">
        {loading && !error ? (
          <Loading />
        ) : powerHour && powerHour.entries?.length > 0 ? (
        <>
          <div className="text-xl uppercase">
            {powerHour?.title}{" "}
            <span className="text-xl uppercase">
              ({powerHour.entries?.length} songs)
            </span>
          </div>

          <div className="overflow-auto flex-grow w-full p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 gap-4">
              {powerHour.entries?.map((entry) => (
                <PowerHourEntryCard entry={entry} key={entry.id} />
              ))}
            </div>
          </div>

          <div className="flex w-full items-center justify-center bg-gray-900 p-4">
            <Button onClick={onStartThisPowerHour}>
              Start This Power Hour
            </Button>
          </div>
        </>
      ) : error === null ? (
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-center p-8">
            <h3 className="text-xl font-bold mb-2">No Power Hour Found</h3>
            <p className="text-gray-400 mb-4">
              The power hour you're looking for doesn't exist or hasn't been created yet.
            </p>
            <Button onClick={() => router.push('/power-hour-ai')}>
              Go Back
            </Button>
          </div>
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center p-8">
          <div className="max-w-md w-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold mb-2 text-red-400">Error Loading Power Hour</h3>
              <p className="text-gray-400 mb-4">
                There was a problem loading this power hour.
              </p>
            </div>

            <FirestoreStatus
              error={error}
              connectionError={connectionError}
              firestoreError={firestoreError}
              loading={loading}
              showDetails={true}
            />

            <div className="flex gap-3 justify-center mt-6">
              <Button
                onClick={() => window.location.reload()}
                color="primary"
              >
                Retry
              </Button>
              <Button
                onClick={() => router.push('/power-hour-ai')}
                variant="bordered"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </ClientOnly>
  );
};

export default PowerHourDetails;
